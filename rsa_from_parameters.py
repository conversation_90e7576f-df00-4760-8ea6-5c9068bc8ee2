#!/usr/bin/env python3
"""
Create RSA keys directly from the n, e, d parameters in brut.txt
"""

import os
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP

def extract_parameters_from_brut(filename="brut.txt"):
    """
    Extract n, e, d parameters directly from brut.txt
    """
    with open(filename, 'r') as f:
        content = f.read()
    
    # Split by sections
    sections = content.split('\n\n')
    
    # Find the parameters
    n = None
    e = None
    d = None
    
    for i, section in enumerate(sections):
        lines = section.strip().split('\n')
        if not lines:
            continue
            
        if lines[0] == 'pexp':
            # Private exponent (d)
            d = int(''.join(lines[1:]))
        elif lines[0] == 'mod':
            # Modulus (n)
            n = int(''.join(lines[1:]))
        elif lines[0] == 'e':
            # Public exponent (e)
            e = int(''.join(lines[1:]))
    
    return n, e, d

def create_rsa_key_from_parameters(n, e, d):
    """
    Create RSA key from n, e, d parameters
    """
    try:
        # Create RSA key with just n, e, d
        # PyCrypto will work with these basic parameters
        rsa_key = RSA.construct((n, e, d))
        print("✅ RSA key created successfully!")
        return rsa_key
    except Exception as ex:
        print(f"❌ Error creating RSA key: {ex}")
        return None

def test_rsa_key(rsa_key, test_filename="ciphtest.txt"):
    """
    Test the RSA key by encrypting and decrypting test content
    """
    # Read test content
    if os.path.exists(test_filename):
        with open(test_filename, 'r') as f:
            test_content = f.read().strip()
    else:
        test_content = "This is a test message."
    
    print(f"Testing with content: '{test_content}'")
    
    try:
        # Create cipher objects
        public_key = rsa_key.publickey()
        cipher_encrypt = PKCS1_OAEP.new(public_key)
        cipher_decrypt = PKCS1_OAEP.new(rsa_key)
        
        # Encrypt
        test_bytes = test_content.encode('utf-8')
        ciphertext = cipher_encrypt.encrypt(test_bytes)
        print(f"✅ Encryption successful. Ciphertext: {len(ciphertext)} bytes")
        print(f"Ciphertext (hex): {ciphertext.hex()}")
        
        # Decrypt
        decrypted_bytes = cipher_decrypt.decrypt(ciphertext)
        decrypted_text = decrypted_bytes.decode('utf-8')
        print(f"✅ Decryption successful. Decrypted: '{decrypted_text}'")
        
        # Verify
        if test_content == decrypted_text:
            print("✅ Round-trip test PASSED!")
            return True
        else:
            print("❌ Round-trip test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def save_pem_files(rsa_key):
    """
    Save the RSA key in PEM format
    """
    try:
        # Export private key
        private_pem = rsa_key.export_key('PEM')
        with open('final_private.pem', 'wb') as f:
            f.write(private_pem)
        
        # Export public key
        public_pem = rsa_key.publickey().export_key('PEM')
        with open('final_public.pem', 'wb') as f:
            f.write(public_pem)
        
        print("✅ Keys saved to final_private.pem and final_public.pem")
        return True
        
    except Exception as e:
        print(f"❌ Error saving PEM files: {e}")
        return False

def display_key_info(rsa_key):
    """
    Display information about the RSA key
    """
    print("\nRSA Key Information:")
    print("-" * 30)
    print(f"Key size: {rsa_key.size_in_bits()} bits")
    print(f"Modulus (n): {rsa_key.n}")
    print(f"Public exponent (e): {rsa_key.e}")
    print(f"Private exponent (d): {rsa_key.d}")
    print()

def main():
    print("RSA Key Creator from Direct Parameters")
    print("=" * 45)
    
    try:
        # Extract parameters
        print("Extracting RSA parameters from brut.txt...")
        n, e, d = extract_parameters_from_brut()
        
        if not all([n, e, d]):
            print("❌ Could not extract all required parameters (n, e, d)")
            print(f"n: {'✅' if n else '❌'}")
            print(f"e: {'✅' if e else '❌'}")
            print(f"d: {'✅' if d else '❌'}")
            return
        
        print("✅ Successfully extracted all parameters:")
        print(f"Modulus (n): {n.bit_length()} bits")
        print(f"Public exponent (e): {e}")
        print(f"Private exponent (d): {d.bit_length()} bits")
        print()
        
        # Create RSA key
        print("Creating RSA key from parameters...")
        rsa_key = create_rsa_key_from_parameters(n, e, d)
        
        if not rsa_key:
            print("❌ Failed to create RSA key")
            return
        
        # Display key information
        display_key_info(rsa_key)
        
        # Save PEM files
        print("Saving PEM files...")
        save_pem_files(rsa_key)
        
        # Test the key
        print("\nTesting RSA key with ciphtest.txt...")
        print("-" * 40)
        success = test_rsa_key(rsa_key)
        
        if success:
            print("\n🎉 SUCCESS! RSA key created and tested successfully!")
            print("You can now use final_private.pem and final_public.pem")
            print("\nTo use with OpenSSL:")
            print("  openssl rsa -in final_private.pem -text -noout")
            print("  openssl rsa -in final_private.pem -pubout -out final_public_openssl.pem")
        else:
            print("\n⚠️  Key was created but testing failed.")
            
    except FileNotFoundError:
        print("❌ Error: brut.txt not found")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
