#!/usr/bin/env python3
"""
RSA Key Direct Reconstructor
Directly reconstructs RSA keys from the ASN.1 parsed parameters without factorization
"""

import os
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import base64

def extract_asn1_parameters():
    """
    Extract RSA parameters from the ASN.1 structure shown in the image.
    Based on the visible parameters in the screenshot.
    """
    
    # From the ASN.1 structure visible in the image:
    # These are the hex values that can be seen
    
    # Modulus (n) - 4096 bits
    n_hex = "674971743814619320240222811170337501517193284239680837578391606609466440267058807747819476281175041059157526943524643706285335955460454435164557505449872260211332548616779747512445412893177564661760540765209518472524003835825045293879918143023475378498386688406339579749717933603124176673885532256128059519238449150108413404160178871557098650077509528231785630435450721447068209068057552356460897142386491394754795875643953099150487831461916472370989535115481694789457608704666737511755271621840180654527128536469132646163810260126467648200976625243333403486177466884240612069783037848387995355369169793659082422886954711676924477258181590400154702410999491541433345950880938367763895511336301501714644403772467696641723178473585043112248936175603229334616206338411665785564730417844797896352036929549059574668030944717805009049933693759967118766343368310362739502515277273256403445968813979576624249588866423381207691575997073762589604280399581330837564713368872908900534013190693982079647557224240992739544008489790753123738542443159909188539510947213409403306019479517504743718003349785933011228636459741216342537267773333146685808930745895017335897831381461662791406837322540527936786812199942643314882789525335149005506351503897"
    
    # Public exponent (e)
    e = 65537
    
    # Private exponent (d) - from brut.txt
    d_hex = "75614663039488506062051754760840771532898110132096476027219034694537077088296876331439066747361826213836491876779376436380726168698540120941968439741287835885668815407721131708316449727407388689611412945825604367539896099335459426232580435728069308856262001128267915865464704728206010568344343318693900336265116445111280025992700500969671541695015879506244524484186485162672852277334572610559825559378224092792319584355437086848169691829751412066822770324207802558972330457620901414551342316587372971746285724390974267016720677142974180552510295685667549937209075976432458030855826489415241089410058126250279806553510602760234276320311734623272099125209829635475088916792487323209763008323059931813410871817597575642336618426774730574399546298727366859792486886322218820135219037160164620473277721209449041969262821216952505060176956086972863776470075594442540075234699511641070409094506953754684156506583840796409328071139724058764492146932750414979477644727483843802367696262739137318341140090873985634115036275034903662652984649948572062098107495696766564470918009386062110290315274102674265343768037755510572448236033168188314900078119695638266344701317080876930533784997566106863818767572827371672620876029959310242928341360673"
    
    # From the ASN.1 structure, we can see prime1 and prime2 values
    # These would be p and q respectively
    # Let me extract the visible hex values from the structure
    
    # Prime1 (p) - 2048 bits - from ASN.1 structure
    p_hex = "279129205362144865367215022590411246860130340786327007001896900270097500"  # This is partial, need full value
    
    # Prime2 (q) - 2048 bits - from ASN.1 structure  
    q_hex = "418133720206365020260119712800611747468351449978210025383856565755"  # This is partial, need full value
    
    # For now, let's work with n, e, d and calculate p, q if needed
    n = int(n_hex)
    d = int(d_hex)
    
    return n, e, d

def calculate_missing_parameters(n, e, d):
    """
    Calculate missing RSA parameters from n, e, d using mathematical relationships.
    """
    # We can derive p and q from n, e, d using the relationship ed ≡ 1 (mod φ(n))
    # This is the same factorization we were trying before, but let's use a simpler approach
    
    # For testing purposes, let's see if we can work with just n, e, d
    # and calculate the CRT parameters later if needed
    
    print("Calculating missing RSA parameters...")
    
    # Try to factor n using the relationship with e and d
    k = e * d - 1
    
    # Find t such that k = 2^t * r where r is odd
    t = 0
    r = k
    while r % 2 == 0:
        r //= 2
        t += 1
    
    print(f"k = ed - 1 = 2^{t} * r")
    
    # Try to find factors using probabilistic method
    import random
    for _ in range(100):
        g = random.randint(2, n-1)
        
        if pow(g, r, n) == 1:
            continue
            
        y = pow(g, r, n)
        if y == n - 1:
            continue
            
        for _ in range(t-1):
            x = y
            y = pow(y, 2, n)
            
            if y == 1:
                p = pow(x - 1, 1, n)  # gcd(x-1, n)
                import math
                p = math.gcd(x - 1, n)
                if 1 < p < n:
                    q = n // p
                    return p, q
                    
            if y == n - 1:
                break
    
    # If factorization fails, we can still create a key with just n, e, d
    return None, None

def create_rsa_key_direct(n, e, d, p=None, q=None):
    """
    Create RSA key directly from parameters.
    """
    try:
        if p and q:
            # Full key with all parameters
            key = RSA.construct((n, e, d, p, q))
        else:
            # Key with just n, e, d (some operations may be slower)
            key = RSA.construct((n, e, d))
        
        return key
    except Exception as e:
        print(f"Error creating RSA key: {e}")
        return None

def test_rsa_key(rsa_key, test_filename="ciphtest.txt"):
    """
    Test the RSA key by encrypting and decrypting test content.
    """
    # Read test content
    if os.path.exists(test_filename):
        with open(test_filename, 'r') as f:
            test_content = f.read().strip()
    else:
        test_content = "This is a test message."
    
    print(f"Testing with content: '{test_content}'")
    
    try:
        # Create cipher objects
        public_key = rsa_key.publickey()
        cipher_encrypt = PKCS1_OAEP.new(public_key)
        cipher_decrypt = PKCS1_OAEP.new(rsa_key)
        
        # Encrypt
        test_bytes = test_content.encode('utf-8')
        ciphertext = cipher_encrypt.encrypt(test_bytes)
        print(f"✅ Encryption successful. Ciphertext: {len(ciphertext)} bytes")
        
        # Decrypt
        decrypted_bytes = cipher_decrypt.decrypt(ciphertext)
        decrypted_text = decrypted_bytes.decode('utf-8')
        print(f"✅ Decryption successful. Decrypted: '{decrypted_text}'")
        
        # Verify
        if test_content == decrypted_text:
            print("✅ Round-trip test PASSED!")
            return True
        else:
            print("❌ Round-trip test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    print("RSA Key Direct Reconstructor")
    print("=" * 50)
    
    try:
        # Extract parameters
        print("Extracting RSA parameters...")
        n, e, d = extract_asn1_parameters()
        
        print(f"Modulus (n): {n.bit_length()} bits")
        print(f"Public exponent (e): {e}")
        print(f"Private exponent (d): {d.bit_length()} bits")
        print()
        
        # Try to calculate p and q
        print("Attempting to calculate prime factors...")
        p, q = calculate_missing_parameters(n, e, d)
        
        if p and q:
            print(f"✅ Found prime factors:")
            print(f"p: {p.bit_length()} bits")
            print(f"q: {q.bit_length()} bits")
        else:
            print("⚠️  Could not factor n, proceeding with n, e, d only")
        
        # Create RSA key
        print("\nCreating RSA key...")
        rsa_key = create_rsa_key_direct(n, e, d, p, q)
        
        if not rsa_key:
            print("❌ Failed to create RSA key")
            return
        
        print("✅ RSA key created successfully!")
        
        # Export keys
        private_pem = rsa_key.export_key('PEM')
        public_pem = rsa_key.publickey().export_key('PEM')
        
        # Save keys
        with open('reconstructed_private.pem', 'wb') as f:
            f.write(private_pem)
        with open('reconstructed_public.pem', 'wb') as f:
            f.write(public_pem)
        
        print("Keys saved to reconstructed_private.pem and reconstructed_public.pem")
        print()
        
        # Test the key
        print("Testing reconstructed RSA key...")
        print("-" * 40)
        success = test_rsa_key(rsa_key)
        
        if success:
            print("\n🎉 SUCCESS! RSA key reconstructed and tested successfully!")
        else:
            print("\n⚠️  Key was created but testing failed.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
