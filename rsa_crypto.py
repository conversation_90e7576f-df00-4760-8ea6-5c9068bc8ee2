#!/usr/bin/env python3
"""
RSA Encryption/Decryption Script
Implements RSA encryption and decryption using parameters from brut.txt
"""

import sys
import os
from typing import Tuple, Optional


def parse_rsa_parameters(filename: str) -> Tuple[int, int, int]:
    """
    Parse RSA parameters from the given file.
    
    Args:
        filename: Path to the file containing RSA parameters
        
    Returns:
        Tuple of (e, d, n) where:
        - e: public exponent
        - d: private exponent  
        - n: modulus
    """
    try:
        with open(filename, 'r') as f:
            lines = f.readlines()
        
        e = d = n = None
        
        for i, line in enumerate(lines):
            line = line.strip()
            if line == "e" and i + 1 < len(lines):
                e = int(lines[i + 1].strip())
            elif line == "pexp" and i + 1 < len(lines):
                d = int(lines[i + 1].strip())
            elif line == "mod" and i + 1 < len(lines):
                n = int(lines[i + 1].strip())
        
        if e is None or d is None or n is None:
            raise ValueError("Could not find all required RSA parameters (e, d, n)")
            
        return e, d, n
        
    except FileNotFoundError:
        raise FileNotFoundError(f"RSA parameters file '{filename}' not found")
    except ValueError as ve:
        raise ValueError(f"Error parsing RSA parameters: {ve}")


def text_to_int(text: str) -> int:
    """
    Convert text to integer using UTF-8 encoding.
    
    Args:
        text: Text to convert
        
    Returns:
        Integer representation of the text
    """
    return int.from_bytes(text.encode('utf-8'), byteorder='big')


def int_to_text(num: int) -> str:
    """
    Convert integer back to text using UTF-8 encoding.

    Args:
        num: Integer to convert

    Returns:
        Text representation of the integer
    """
    # Calculate the number of bytes needed
    byte_length = (num.bit_length() + 7) // 8
    if byte_length == 0:
        byte_length = 1

    try:
        byte_data = num.to_bytes(byte_length, byteorder='big')
        # Debug: print byte data
        print(f"Debug: Converting integer {num} to bytes: {byte_data}")
        return byte_data.decode('utf-8')
    except UnicodeDecodeError as e:
        print(f"Debug: UnicodeDecodeError - {e}")
        print(f"Debug: Byte length: {byte_length}")
        print(f"Debug: Raw bytes: {num.to_bytes(byte_length, byteorder='big')}")
        raise ValueError(f"Cannot decode integer to valid UTF-8 text: {e}")


def rsa_encrypt(plaintext: int, e: int, n: int) -> int:
    """
    Encrypt plaintext using RSA public key.
    
    Args:
        plaintext: Integer representation of plaintext
        e: Public exponent
        n: Modulus
        
    Returns:
        Encrypted ciphertext as integer
    """
    if plaintext >= n:
        raise ValueError("Plaintext too large for modulus")
    
    return pow(plaintext, e, n)


def rsa_decrypt(ciphertext: int, d: int, n: int) -> int:
    """
    Decrypt ciphertext using RSA private key.
    
    Args:
        ciphertext: Integer representation of ciphertext
        d: Private exponent
        n: Modulus
        
    Returns:
        Decrypted plaintext as integer
    """
    return pow(ciphertext, d, n)


def encrypt_text(text: str, e: int, n: int) -> int:
    """
    Encrypt text message using RSA.
    
    Args:
        text: Text to encrypt
        e: Public exponent
        n: Modulus
        
    Returns:
        Encrypted message as integer
    """
    plaintext_int = text_to_int(text)
    return rsa_encrypt(plaintext_int, e, n)


def decrypt_text(ciphertext: int, d: int, n: int) -> str:
    """
    Decrypt integer ciphertext back to text using RSA.

    Args:
        ciphertext: Encrypted message as integer
        d: Private exponent
        n: Modulus

    Returns:
        Decrypted text message
    """
    plaintext_int = rsa_decrypt(ciphertext, d, n)
    print(f"Debug: Decrypted integer: {plaintext_int}")
    return int_to_text(plaintext_int)


def test_basic_rsa(e: int, d: int, n: int) -> bool:
    """Test RSA with a simple integer to verify the parameters work."""
    test_int = 42
    print(f"Testing RSA with simple integer: {test_int}")

    # Encrypt
    encrypted = pow(test_int, e, n)
    print(f"Encrypted: {encrypted}")

    # Decrypt
    decrypted = pow(encrypted, d, n)
    print(f"Decrypted: {decrypted}")

    success = test_int == decrypted
    print(f"Basic RSA test {'PASSED' if success else 'FAILED'}")
    return success


def test_rsa_with_file(test_filename: str, rsa_params_filename: str = "brut.txt") -> bool:
    """
    Test RSA encryption/decryption with content from a file.
    
    Args:
        test_filename: File containing test text
        rsa_params_filename: File containing RSA parameters
        
    Returns:
        True if test passes, False otherwise
    """
    try:
        # Load RSA parameters
        e, d, n = parse_rsa_parameters(rsa_params_filename)
        print(f"Loaded RSA parameters:")
        print(f"  Public exponent (e): {e}")
        print(f"  Private exponent (d): {d}")
        print(f"  Modulus (n): {n}")
        print(f"  Modulus bit length: {n.bit_length()}")
        print()

        # First test basic RSA functionality
        print("=== Basic RSA Test ===")
        if not test_basic_rsa(e, d, n):
            print("Basic RSA test failed - RSA parameters may be incorrect!")
            return False
        print()
        
        # Load test text
        with open(test_filename, 'r') as f:
            original_text = f.read().strip()
        
        print(f"Original text: '{original_text}'")
        
        # Convert to integer and check size
        plaintext_int = text_to_int(original_text)
        print(f"Text as integer: {plaintext_int}")
        print(f"Text integer bit length: {plaintext_int.bit_length()}")
        
        if plaintext_int >= n:
            print("ERROR: Text is too large for this RSA key!")
            return False
        
        # Encrypt
        print("\nEncrypting...")
        ciphertext = encrypt_text(original_text, e, n)
        print(f"Ciphertext: {ciphertext}")
        
        # Decrypt
        print("\nDecrypting...")
        decrypted_text = decrypt_text(ciphertext, d, n)
        print(f"Decrypted text: '{decrypted_text}'")
        
        # Verify
        success = original_text == decrypted_text
        print(f"\nTest {'PASSED' if success else 'FAILED'}")
        if not success:
            print(f"Expected: '{original_text}'")
            print(f"Got:      '{decrypted_text}'")
        
        return success
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        return False


def main():
    """Main function with command-line interface."""
    if len(sys.argv) < 2:
        print("RSA Encryption/Decryption Tool")
        print("Usage:")
        print("  python rsa_crypto.py test [test_file] [rsa_params_file]")
        print("  python rsa_crypto.py encrypt <text> [rsa_params_file]")
        print("  python rsa_crypto.py decrypt <ciphertext_int> [rsa_params_file]")
        print()
        print("Examples:")
        print("  python rsa_crypto.py test ciphtest.txt brut.txt")
        print("  python rsa_crypto.py encrypt 'Hello World'")
        print("  python rsa_crypto.py decrypt 12345678901234567890")
        return
    
    command = sys.argv[1].lower()
    rsa_params_file = sys.argv[-1] if len(sys.argv) > 3 else "brut.txt"
    
    try:
        if command == "test":
            test_file = sys.argv[2] if len(sys.argv) > 2 else "ciphtest.txt"
            test_rsa_with_file(test_file, rsa_params_file)
            
        elif command == "encrypt":
            if len(sys.argv) < 3:
                print("Error: Please provide text to encrypt")
                return
            
            text = sys.argv[2]
            e, d, n = parse_rsa_parameters(rsa_params_file)
            ciphertext = encrypt_text(text, e, n)
            print(f"Encrypted: {ciphertext}")
            
        elif command == "decrypt":
            if len(sys.argv) < 3:
                print("Error: Please provide ciphertext integer to decrypt")
                return
            
            ciphertext = int(sys.argv[2])
            e, d, n = parse_rsa_parameters(rsa_params_file)
            plaintext = decrypt_text(ciphertext, d, n)
            print(f"Decrypted: '{plaintext}'")
            
        else:
            print(f"Unknown command: {command}")
            
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
