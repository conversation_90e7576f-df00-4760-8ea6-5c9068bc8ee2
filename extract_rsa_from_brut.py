#!/usr/bin/env python3
"""
Extract RSA key from brut.txt and create usable PEM files
"""

import base64
import os
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP

def extract_base64_from_brut(filename="brut.txt"):
    """
    Extract the base64 encoded RSA private key from brut.txt
    """
    with open(filename, 'r') as f:
        lines = f.readlines()

    # Find the base64 content (starts with MII...)
    base64_lines = []
    in_base64 = False

    for line in lines:
        line = line.strip()

        # Start collecting when we see the base64 header
        if line.startswith('MII'):
            in_base64 = True

        # Stop when we hit empty lines or non-base64 content
        if in_base64:
            if not line:  # Empty line
                break
            # Check if this looks like base64
            if all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in line):
                base64_lines.append(line)
            else:
                # Stop when we hit non-base64 content
                break

    # Join all base64 lines
    base64_content = ''.join(base64_lines)
    return base64_content

def decode_rsa_private_key(base64_content):
    """
    Decode the base64 content and create an RSA private key
    """
    try:
        # Try different padding strategies for base64 decoding
        for padding in range(4):
            try:
                padded_content = base64_content + ('=' * padding)
                der_bytes = base64.b64decode(padded_content, validate=True)
                print(f"Successfully decoded {len(der_bytes)} bytes of DER data (padding: {padding})")
                break
            except Exception:
                continue
        else:
            raise ValueError("Could not decode base64 with any padding strategy")

        # Show hex dump of first few bytes to understand the structure
        print(f"DER hex dump (first 32 bytes): {der_bytes[:32].hex()}")

        # Try different key formats
        formats_to_try = [
            ("PKCS#1 RSA private key", lambda: RSA.import_key(der_bytes)),
            ("PKCS#8 private key", lambda: RSA.import_key(der_bytes, passphrase=None)),
            ("PEM format", lambda: RSA.import_key(der_bytes.decode('utf-8'))),
        ]

        for format_name, import_func in formats_to_try:
            try:
                print(f"Trying {format_name}...")
                rsa_key = import_func()
                print(f"✅ Successfully parsed as {format_name}!")
                return rsa_key
            except Exception as e:
                print(f"  Failed: {e}")
                continue

        # If all formats fail, let's try to manually parse the ASN.1 structure
        print("All standard formats failed. Analyzing ASN.1 structure...")
        analyze_der_structure(der_bytes)

        return None

    except Exception as e:
        print(f"❌ Error decoding RSA key: {e}")
        return None

def analyze_der_structure(der_bytes):
    """
    Analyze the DER/ASN.1 structure to understand what we have
    """
    print(f"DER data length: {len(der_bytes)} bytes")
    print(f"First 64 bytes (hex): {der_bytes[:64].hex()}")

    # Check if it starts with a SEQUENCE tag (0x30)
    if der_bytes[0] == 0x30:
        print("✅ Starts with SEQUENCE tag (0x30) - looks like ASN.1")

        # Parse length
        if der_bytes[1] & 0x80 == 0:
            length = der_bytes[1]
            data_start = 2
        else:
            length_bytes = der_bytes[1] & 0x7F
            length = 0
            for i in range(length_bytes):
                length = (length << 8) | der_bytes[2 + i]
            data_start = 2 + length_bytes

        print(f"SEQUENCE length: {length} bytes")
        print(f"Data starts at byte: {data_start}")

        # Look for INTEGER tags (0x02) which should contain RSA parameters
        pos = data_start
        integers_found = []

        while pos < len(der_bytes) - 1:
            if der_bytes[pos] == 0x02:  # INTEGER tag
                pos += 1
                # Parse integer length
                if der_bytes[pos] & 0x80 == 0:
                    int_length = der_bytes[pos]
                    int_start = pos + 1
                else:
                    length_bytes = der_bytes[pos] & 0x7F
                    int_length = 0
                    for i in range(length_bytes):
                        int_length = (int_length << 8) | der_bytes[pos + 1 + i]
                    int_start = pos + 1 + length_bytes

                # Extract integer value
                int_bytes = der_bytes[int_start:int_start + int_length]
                int_value = int.from_bytes(int_bytes, 'big')
                integers_found.append((int_length, int_value))

                print(f"Found INTEGER: {int_length} bytes")
                if int_length > 8:  # Only show hex for large integers
                    print(f"  Hex: {int_bytes.hex()[:32]}...")
                    print(f"  Bit length: {int_value.bit_length()}")
                else:
                    print(f"  Value: {int_value}")

                pos = int_start + int_length
            else:
                pos += 1

        print(f"Total integers found: {len(integers_found)}")

        # RSA private key should have 9 integers: version, n, e, d, p, q, dp, dq, qinv
        if len(integers_found) >= 9:
            print("✅ Found enough integers for RSA private key format")
            return try_manual_rsa_construction(integers_found)
        else:
            print("❌ Not enough integers for standard RSA private key")
    else:
        print(f"❌ Does not start with SEQUENCE tag (found: 0x{der_bytes[0]:02x})")

    return None

def try_manual_rsa_construction(integers_found):
    """
    Try to manually construct RSA key from parsed integers
    """
    try:
        if len(integers_found) >= 9:
            # Standard RSA private key format: version, n, e, d, p, q, dp, dq, qinv
            version = integers_found[0][1]
            n = integers_found[1][1]
            e = integers_found[2][1]
            d = integers_found[3][1]
            p = integers_found[4][1]
            q = integers_found[5][1]

            print(f"Attempting manual RSA construction:")
            print(f"  Version: {version}")
            print(f"  n: {n.bit_length()} bits")
            print(f"  e: {e}")
            print(f"  d: {d.bit_length()} bits")
            print(f"  p: {p.bit_length()} bits")
            print(f"  q: {q.bit_length()} bits")

            # Construct RSA key manually
            rsa_key = RSA.construct((n, e, d, p, q))
            print("✅ Manual RSA construction successful!")
            return rsa_key

    except Exception as e:
        print(f"❌ Manual RSA construction failed: {e}")

    return None

def test_rsa_key(rsa_key, test_filename="ciphtest.txt"):
    """
    Test the RSA key with the test file
    """
    # Read test content
    if os.path.exists(test_filename):
        with open(test_filename, 'r') as f:
            test_content = f.read().strip()
    else:
        test_content = "This is a test message."
    
    print(f"Testing with content: '{test_content}'")
    
    try:
        # Create cipher objects
        public_key = rsa_key.publickey()
        cipher_encrypt = PKCS1_OAEP.new(public_key)
        cipher_decrypt = PKCS1_OAEP.new(rsa_key)
        
        # Encrypt
        test_bytes = test_content.encode('utf-8')
        ciphertext = cipher_encrypt.encrypt(test_bytes)
        print(f"✅ Encryption successful. Ciphertext: {len(ciphertext)} bytes")
        print(f"Ciphertext (hex): {ciphertext.hex()}")
        
        # Decrypt
        decrypted_bytes = cipher_decrypt.decrypt(ciphertext)
        decrypted_text = decrypted_bytes.decode('utf-8')
        print(f"✅ Decryption successful. Decrypted: '{decrypted_text}'")
        
        # Verify
        if test_content == decrypted_text:
            print("✅ Round-trip test PASSED!")
            return True
        else:
            print("❌ Round-trip test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def save_pem_files(rsa_key):
    """
    Save the RSA key in PEM format
    """
    try:
        # Export private key
        private_pem = rsa_key.export_key('PEM')
        with open('extracted_private.pem', 'wb') as f:
            f.write(private_pem)
        
        # Export public key
        public_pem = rsa_key.publickey().export_key('PEM')
        with open('extracted_public.pem', 'wb') as f:
            f.write(public_pem)
        
        print("✅ Keys saved to extracted_private.pem and extracted_public.pem")
        return True
        
    except Exception as e:
        print(f"❌ Error saving PEM files: {e}")
        return False

def display_key_info(rsa_key):
    """
    Display information about the RSA key
    """
    print("\nRSA Key Information:")
    print("-" * 30)
    print(f"Key size: {rsa_key.size_in_bits()} bits")
    print(f"Modulus (n): {hex(rsa_key.n)}")
    print(f"Public exponent (e): {rsa_key.e}")
    print(f"Private exponent (d): {hex(rsa_key.d)}")
    
    if hasattr(rsa_key, 'p') and rsa_key.p:
        print(f"Prime p: {hex(rsa_key.p)}")
        print(f"Prime q: {hex(rsa_key.q)}")
    
    print()

def main():
    print("RSA Key Extractor from brut.txt")
    print("=" * 40)
    
    try:
        # Extract base64 content
        print("Extracting base64 content from brut.txt...")
        base64_content = extract_base64_from_brut()
        print(f"Extracted {len(base64_content)} characters of base64 data")
        
        # Decode RSA private key
        print("\nDecoding RSA private key...")
        rsa_key = decode_rsa_private_key(base64_content)
        
        if not rsa_key:
            print("❌ Failed to decode RSA key")
            return
        
        # Display key information
        display_key_info(rsa_key)
        
        # Save PEM files
        print("Saving PEM files...")
        save_pem_files(rsa_key)
        
        # Test the key
        print("\nTesting RSA key with ciphtest.txt...")
        print("-" * 40)
        success = test_rsa_key(rsa_key)
        
        if success:
            print("\n🎉 SUCCESS! RSA key extracted and tested successfully!")
            print("You can now use extracted_private.pem and extracted_public.pem")
        else:
            print("\n⚠️  Key was extracted but testing failed.")
            
    except FileNotFoundError:
        print("❌ Error: brut.txt not found")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
